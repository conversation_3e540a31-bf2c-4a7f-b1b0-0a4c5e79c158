package io.gigsta.presentation.email.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ContentCopy
import androidx.compose.material.icons.filled.Edit
import androidx.compose.material.icons.filled.Save
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalClipboardManager
import androidx.compose.ui.text.AnnotatedString
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import io.gigsta.presentation.components.common.ErrorMessage
import io.gigsta.presentation.theme.Spacing

/**
 * Component for displaying and editing email results
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun EmailResultSection(
    isGenerating: Boolean,
    emailSubject: String,
    emailBody: String,
    isSubjectEditable: Boolean,
    isBodyEditable: Boolean,
    isSaving: Boolean,
    error: String?,
    onSubjectChanged: (String) -> Unit,
    onBodyChanged: (String) -> Unit,
    onEditSubject: () -> Unit,
    onEditBody: () -> Unit,
    onSaveChanges: () -> Unit,
    onCopySubject: () -> Unit,
    onCopyBody: () -> Unit,
    onCopyBoth: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier.fillMaxWidth(),
        verticalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        Text(
            text = "3. Hasil Email Lamaran",
            style = MaterialTheme.typography.headlineSmall,
            fontWeight = FontWeight.SemiBold
        )
        
        if (isGenerating) {
            GeneratingCard()
        } else if (emailSubject.isNotEmpty() || emailBody.isNotEmpty()) {
            // Email Subject Section
            EmailSubjectCard(
                subject = emailSubject,
                isEditable = isSubjectEditable,
                onSubjectChanged = onSubjectChanged,
                onEditSubject = onEditSubject,
                onCopySubject = onCopySubject
            )
            
            // Email Body Section
            EmailBodyCard(
                body = emailBody,
                isEditable = isBodyEditable,
                onBodyChanged = onBodyChanged,
                onEditBody = onEditBody,
                onCopyBody = onCopyBody
            )
            
            // Action Buttons
            ActionButtonsRow(
                isSaving = isSaving,
                hasChanges = isSubjectEditable || isBodyEditable,
                onSaveChanges = onSaveChanges,
                onCopyBoth = onCopyBoth
            )
        } else {
            EmptyResultCard()
        }
        
        error?.let { errorMessage ->
            ErrorMessage(message = errorMessage)
        }
    }
}

@Composable
private fun GeneratingCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.large),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            CircularProgressIndicator(
                modifier = Modifier.size(48.dp),
                strokeWidth = 4.dp
            )
            
            Text(
                text = "Membuat Email Lamaran...",
                style = MaterialTheme.typography.titleMedium,
                fontWeight = FontWeight.Medium
            )
            
            Text(
                text = "AI sedang menganalisis CV dan informasi pekerjaan Anda untuk membuat email lamaran yang dipersonalisasi.",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
        }
    }
}

@Composable
private fun EmptyResultCard() {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surfaceVariant
        )
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(Spacing.large),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.spacedBy(Spacing.medium)
        ) {
            Text(
                text = "Email lamaran Anda akan muncul di sini",
                style = MaterialTheme.typography.titleMedium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
            
            Text(
                text = "Lengkapi langkah 1 dan 2, lalu klik tombol 'Buat Email Lamaran' untuk memulai.",
                style = MaterialTheme.typography.bodySmall,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EmailSubjectCard(
    subject: String,
    isEditable: Boolean,
    onSubjectChanged: (String) -> Unit,
    onEditSubject: () -> Unit,
    onCopySubject: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF0F9FF)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(Spacing.medium),
            verticalArrangement = Arrangement.spacedBy(Spacing.small)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Subjek Email:",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
                ) {
                    IconButton(
                        onClick = onCopySubject,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ContentCopy,
                            contentDescription = "Copy Subject",
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                    
                    IconButton(
                        onClick = onEditSubject,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = "Edit Subject",
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
            
            if (isEditable) {
                OutlinedTextField(
                    value = subject,
                    onValueChange = onSubjectChanged,
                    modifier = Modifier.fillMaxWidth(),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Text
                    ),
                    shape = RoundedCornerShape(8.dp)
                )
            } else {
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White.copy(alpha = 0.7f)
                    )
                ) {
                    Text(
                        text = subject,
                        modifier = Modifier.padding(Spacing.medium),
                        style = MaterialTheme.typography.bodyMedium,
                        fontWeight = FontWeight.Medium
                    )
                }
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun EmailBodyCard(
    body: String,
    isEditable: Boolean,
    onBodyChanged: (String) -> Unit,
    onEditBody: () -> Unit,
    onCopyBody: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = Color(0xFFF0F9FF)
        ),
        shape = RoundedCornerShape(12.dp)
    ) {
        Column(
            modifier = Modifier.padding(Spacing.medium),
            verticalArrangement = Arrangement.spacedBy(Spacing.small)
        ) {
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = "Isi Email:",
                    style = MaterialTheme.typography.labelMedium,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.primary
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(Spacing.extraSmall)
                ) {
                    IconButton(
                        onClick = onCopyBody,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.ContentCopy,
                            contentDescription = "Copy Body",
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                    
                    IconButton(
                        onClick = onEditBody,
                        modifier = Modifier.size(32.dp)
                    ) {
                        Icon(
                            imageVector = Icons.Default.Edit,
                            contentDescription = "Edit Body",
                            modifier = Modifier.size(16.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                }
            }
            
            if (isEditable) {
                OutlinedTextField(
                    value = body,
                    onValueChange = onBodyChanged,
                    modifier = Modifier
                        .fillMaxWidth()
                        .heightIn(min = 200.dp),
                    keyboardOptions = KeyboardOptions(
                        keyboardType = KeyboardType.Text
                    ),
                    maxLines = 15,
                    shape = RoundedCornerShape(8.dp)
                )
            } else {
                Card(
                    colors = CardDefaults.cardColors(
                        containerColor = Color.White.copy(alpha = 0.7f)
                    )
                ) {
                    Text(
                        text = body,
                        modifier = Modifier.padding(Spacing.medium),
                        style = MaterialTheme.typography.bodySmall,
                        lineHeight = MaterialTheme.typography.bodySmall.lineHeight * 1.4
                    )
                }
            }
        }
    }
}

@Composable
private fun ActionButtonsRow(
    isSaving: Boolean,
    hasChanges: Boolean,
    onSaveChanges: () -> Unit,
    onCopyBoth: () -> Unit
) {
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.spacedBy(Spacing.medium)
    ) {
        if (hasChanges) {
            Button(
                onClick = onSaveChanges,
                enabled = !isSaving,
                modifier = Modifier.weight(1f)
            ) {
                if (isSaving) {
                    CircularProgressIndicator(
                        modifier = Modifier.size(16.dp),
                        strokeWidth = 2.dp,
                        color = MaterialTheme.colorScheme.onPrimary
                    )
                    Spacer(modifier = Modifier.width(Spacing.extraSmall))
                    Text("Menyimpan...")
                } else {
                    Icon(
                        imageVector = Icons.Default.Save,
                        contentDescription = null,
                        modifier = Modifier.size(16.dp)
                    )
                    Spacer(modifier = Modifier.width(Spacing.extraSmall))
                    Text("Simpan Perubahan")
                }
            }
        }
        
        OutlinedButton(
            onClick = onCopyBoth,
            modifier = Modifier.weight(1f)
        ) {
            Icon(
                imageVector = Icons.Default.ContentCopy,
                contentDescription = null,
                modifier = Modifier.size(16.dp)
            )
            Spacer(modifier = Modifier.width(Spacing.extraSmall))
            Text("Salin Semua")
        }
    }
}
